// types/user-types.ts

export interface NewUser {
  f_name: string
  l_name: string
  dob: string
  phone_no: number
  email: string
  password?: string
  inst_name: string
  section: string
  qual: string
  usn: string
  yop: string
  branch: string
  subscribed?: boolean
}

export interface UserPasswordChange {
  currentPass: string
  password: string
  conf_password: string
}

export interface BranchCourse {
  data: CourseData[]
}

export interface CourseData {
  bName: string
  subName: string[]
}

export interface UserLoginTrack {
  email: string
  last_ip: string
  last_cookie_val: string
  uag_string: string
  platform: string
  latitude: string
  longitude: string
  os: string
  browser: string
}

export interface CertificationUser {
  email: string
  certificateId: string
  category: string | null
  filename: string
  grantedOn: string
}

export interface UserAccessResponse {
  msg: string
  name?: string
  level?: string
}

export interface BulkGrantResult {
  email: string
  status: string
  message?: string
}

export const BRANCH_COURSE_DATA: BranchCourse = {
  data: [
    {
      bName: 'BE / BTech',
      subName: [
        'Computer Science and Engineering',
        'Artificial Intelligence and Machine Learning',
        'Mechanical Engineering',
        'Electronics & Communication Engineering',
        'Electrical and Electronics Engineering',
        'Civil Engineering',
        'Chemical Engineering',
        'Biochemical Engineering',
        'Aerospace Engineering',
        'Automobile Engineering',
        'Nanotechnology Engineering',
        'Information Technology',
        'Information Science and Engineering',
        'Computer Science and Information Technology',
        'Computer Science and System Engineering',
        'Diploma'
      ]
    },
    {
      bName: 'B.Sc',
      subName: ['BCZMB', 'CBBT', 'CBZ', 'CZBT', 'PBZ', 'PCM', 'PCMS', 'JS']
    },
    {
      bName: 'BBA',
      subName: ['']
    },
    {
      bName: 'BA',
      subName: ['HEP', 'HES', 'PJS', 'SKJ']
    },
    {
      bName: 'B.Voc',
      subName: ['MLT']
    },
    {
      bName: 'BBM',
      subName: ['']
    },
    {
      bName: 'BCA',
      subName: ['Section A', 'Section B', 'Section C', 'Section D']
    },
    {
      bName: 'B.Com',
      subName: ['Section A', 'Section B', 'Section C', 'Section D']
    },
    {
      bName: 'ME / MTech',
      subName: [
        'Aerospace Engineering',
        'Artificial Intelligence and ML',
        'Civil Engineering',
        'Electronics & Communication',
        'Mechanical Engineering',
        'Automobile Engineering',
        'Chemical Engineering',
        'Biotechnology',
        'Signal Processing',
        'VLSI Design',
        'Aeromautical Engineering',
        'Computer Science and Engineering',
        'Information Science',
        'Electrical Engineering',
        'Information Technology',
        'Electronics & Communication Engineering',
        'Design & Maufacture',
        'Embedded Systems'
      ]
    },
    {
      bName: 'MCA',
      subName: ['']
    },
    {
      bName: 'MBA',
      subName: [
        'Finance',
        'IT',
        'Marketing Management',
        'HRM',
        'Rural Management',
        'Operations  Management',
        'Logictics Management',
        'Business Management',
        'Health Care Management',
        'Event Management'
      ]
    },
    {
      bName: 'Other',
      subName: ['']
    }
  ]
}
