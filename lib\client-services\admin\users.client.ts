// lib/client-services/admin/users.client.ts
import axios from 'axios'
import { LoginRefresh } from '../../cookies'
import { NewUser, UserLoginTrack, CertificationUser, UserAccessResponse, BulkGrantResult } from '@/types/user-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class AdminUsersClientService {
  /**
   * Get all registered users with pagination
   */
  static async getAllRegisteredUsers(index: number = 0, take: number = 50): Promise<NewUser[]> {
    try {
      const response = await axios.get(
        `${V2_BASE_URL}/admin/users/${index}/${take}/list`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching registered users:', error)
      return []
    }
  }

  /**
   * Get purchased/not purchased users
   */
  static async getPurchasedUsers(purchased: boolean = true): Promise<NewUser[]> {
    try {
      const option = purchased ? 'all' : 'not'
      const response = await axios.get(
        `${API_BASE_URL}/admin/purchased/${option}`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching purchased users:', error)
      return []
    }
  }

  /**
   * Get users registered in the last week
   */
  static async getLastWeekUsers(): Promise<NewUser[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/register/lastweek`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching last week users:', error)
      return []
    }
  }

  /**
   * Get user login tracking data
   */
  static async getUserLoginData(): Promise<UserLoginTrack[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/users/login/track`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching user login data:', error)
      return []
    }
  }

  /**
   * Get certification tracking data
   */
  static async getCertificationData(): Promise<CertificationUser[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/users/certification/track`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching certification data:', error)
      return []
    }
  }

  /**
   * Get user access level by email
   */
  static async getUserAccess(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/user/access/${email}`,
        createAuthHeaders()
      )
      return response.data || { msg: 'not found' }
    } catch (error) {
      console.error('Error fetching user access:', error)
      return { msg: 'not found' }
    }
  }

  /**
   * Grant user access (elevate to purchased)
   */
  static async grantUserAccess(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/admin/user/grant/${email}`,
        {},
        createAuthHeaders()
      )
      return response.data || { msg: 'error' }
    } catch (error) {
      console.error('Error granting user access:', error)
      return { msg: 'error' }
    }
  }

  /**
   * Bulk grant access to multiple users
   */
  static async bulkGrantAccess(emails: string[]): Promise<BulkGrantResult[]> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/admin/users/bulk/grant`,
        { emails },
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error bulk granting access:', error)
      return []
    }
  }

  /**
   * Search user by email
   */
  static async searchUser(email: string): Promise<NewUser | null> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/user/search/${email}`,
        createAuthHeaders()
      )
      return response.data || null
    } catch (error) {
      console.error('Error searching user:', error)
      return null
    }
  }

  /**
   * Search certification user by email
   */
  static async searchCertificationUser(email: string): Promise<CertificationUser | null> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/user/certification/search/${email}`,
        createAuthHeaders()
      )
      return response.data || null
    } catch (error) {
      console.error('Error searching certification user:', error)
      return null
    }
  }
}
