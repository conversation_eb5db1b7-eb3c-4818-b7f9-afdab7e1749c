// lib/client-services/admin/users.client.ts
import axios from 'axios'
import { LoginRefresh } from '../../cookies'
import { NewUser, UserLoginTrack, CertificationUser, UserAccessResponse, BulkGrantResult } from '@/types/user-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class AdminUsersClientService {
  /**
   * Helper function to process certification data similar to Angular fnSelectCategory
   */
  private static processCertificationData(data: any[]): CertificationUser[] {
    return data.map(item => ({
      email: item.email || '',
      name: item.name || `${item.f_name || ''} ${item.l_name || ''}`.trim(),
      category: item.category || null
    }))
  }
  /**
   * Get all registered users with pagination
   */
  static async getAllRegisteredUsers(index: number = 0, take: number = 50): Promise<NewUser[]> {
    try {
      const response = await axios.get(
        `${V2_BASE_URL}/admin/users/${index}/${take}/list`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching registered users:', error)
      return []
    }
  }

  /**
   * Get purchased/not purchased users
   */
  static async getPurchasedUsers(purchased: boolean = true): Promise<NewUser[]> {
    try {
      const option = purchased ? 'all' : 'not'
      const response = await axios.get(
        `${API_BASE_URL}/admin/purchased/${option}`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching purchased users:', error)
      return []
    }
  }

  /**
   * Get users registered in the last week
   */
  static async getLastWeekUsers(): Promise<NewUser[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/admin/register/lastweek`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching last week users:', error)
      return []
    }
  }

  /**
   * Get user login tracking data with pagination
   */
  static async getUserLoginData(page: number = 1): Promise<UserLoginTrack[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/v2/admin/user/${page}/multiple-login`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching user login data:', error)
      return []
    }
  }

  /**
   * Get certification tracking data with pagination
   */
  static async getCertificationData(page: number = 1): Promise<CertificationUser[]> {
    try {
      const response = await axios.get(
        `${V2_BASE_URL}/admin/internship/${page}/certificates`,
        createAuthHeaders()
      )
      const rawData = response.data || []
      return this.processCertificationData(rawData)
    } catch (error) {
      console.error('Error fetching certification data:', error)
      return []
    }
  }

  /**
   * Get user access level by email
   */
  static async getUserAccess(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/user/manage/${email}/check/access`,
        createAuthHeaders()
      )

      // Parse the response similar to Angular implementation
      const respObj = response.data
      const level = parseInt(respObj.msg, 10)

      let accessLevel = ''
      if (level === 1) {
        accessLevel = 'Admin'
      } else if (level === 2) {
        accessLevel = 'Purchased Basic'
      } else if (level === 4) {
        accessLevel = 'Purchased Premium'
      } else if (level === 3) {
        accessLevel = 'Registered'
      } else if (level === 5) {
        accessLevel = 'Admin Read'
      }

      return {
        msg: respObj.msg,
        level: accessLevel,
        name: '' // Will be filled by the component
      }
    } catch (error: any) {
      console.error('Error fetching user access:', error)
      if (error.response?.data?.msg === 'not found') {
        return { msg: 'not found' }
      }
      return { msg: 'not found' }
    }
  }

  /**
   * Grant user access (elevate to purchased basic)
   */
  static async grantUserAccess(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/user/manage/${email}/grant/access`,
        {},
        createAuthHeaders()
      )
      return response.data || { msg: 'error' }
    } catch (error: any) {
      console.error('Error granting user access:', error)
      if (error.response?.data?.msg === 'not found') {
        return { msg: 'not found' }
      }
      return { msg: 'error' }
    }
  }

  /**
   * Grant user premium access
   */
  static async grantUserAccessPremium(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/user/manage/${email}/grant/access/premium`,
        {},
        createAuthHeaders()
      )
      return response.data || { msg: 'error' }
    } catch (error: any) {
      console.error('Error granting premium access:', error)
      if (error.response?.data?.msg === 'not found') {
        return { msg: 'not found' }
      }
      return { msg: 'error' }
    }
  }

  /**
   * Revoke user access (back to registered)
   */
  static async revokeUserAccess(email: string): Promise<UserAccessResponse> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/user/manage/${email}/revoke/access`,
        {},
        createAuthHeaders()
      )
      return response.data || { msg: 'error' }
    } catch (error: any) {
      console.error('Error revoking user access:', error)
      if (error.response?.data?.msg === 'not found') {
        return { msg: 'not found' }
      }
      return { msg: 'error' }
    }
  }

  /**
   * Bulk grant access to multiple users
   */
  static async bulkGrantAccess(emails: string[]): Promise<BulkGrantResult[]> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/user/manage/grant/access/2`,
        { emails },
        createAuthHeaders()
      )

      // Process the response to match our BulkGrantResult interface
      const results = response.data || []
      return results.map((result: any) => ({
        email: result.email || '',
        status: result.status || 'error',
        message: result.message || result.msg || ''
      }))
    } catch (error) {
      console.error('Error bulk granting access:', error)
      return []
    }
  }

  /**
   * Search user by email
   */
  static async searchUser(email: string): Promise<NewUser | null> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/v2/admin/user/${email}/search`,
        createAuthHeaders()
      )
      return response.data || null
    } catch (error) {
      console.error('Error searching user:', error)
      return null
    }
  }

  /**
   * Search certification user by email - returns certification data for the user
   */
  static async searchCertificationUser(email: string): Promise<any | null> {
    try {
      // First search for the user, then get their certification data
      const user = await this.searchUser(email)
      if (user) {
        // Return user data formatted for certification display
        return {
          email: user.email,
          name: `${user.f_name} ${user.l_name}`,
          category: null // This would need to be determined from certification data
        }
      }
      return null
    } catch (error) {
      console.error('Error searching certification user:', error)
      return null
    }
  }
}
