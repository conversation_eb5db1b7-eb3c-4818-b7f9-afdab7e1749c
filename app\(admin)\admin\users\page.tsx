// app/(admin)/admin/users/page.tsx
import { Metadata } from 'next'
import AdminBreadcrumb from '@/components/admin/admin-breadcrumb'
import UsersClient from '@/components/admin/users/users-client'

export const metadata: Metadata = {
  title: 'User Management | Admin Console',
  description: 'Manage users, view statistics, and handle user access permissions'
}

export default function AdminUsersPage() {
  return (
    <div className="px-4 py-6">
      <AdminBreadcrumb
        items={[
          { label: 'Admin Console', href: '/admin' },
          { label: 'Users', isCurrentPage: true }
        ]}
      />
      
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600">
          View user statistics and manage access permissions
        </p>
      </div>

      <UsersClient />
    </div>
  )
}
