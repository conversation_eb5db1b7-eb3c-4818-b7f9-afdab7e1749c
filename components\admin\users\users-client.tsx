'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Users, UserCheck, UserX, Calendar, Settings, Search, Shield, ShieldCheck, ChevronLeft, ChevronRight } from 'lucide-react'
import { AdminStatsClientService } from '@/lib/client-services/admin/stats.client'
import { AdminUsersClientService } from '@/lib/client-services/admin/users.client'
import { NewUser, UserLoginTrack, CertificationUser } from '@/types/user-types'
import UserAccessModal from './user-access-modal'

interface StatsData {
  regCount: string
  purCount: string
  wkCount: string
}

export default function UsersClient() {
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<StatsData>({
    regCount: '0',
    purCount: '0',
    wkCount: '0'
  })
  
  const [selectedView, setSelectedView] = useState<'all' | 'purchased' | 'lastweek' | 'login' | 'certification'>('all')
  const [purchasedBoxText, setPurchasedBoxText] = useState('Total Purchases')
  const [showPurchased, setShowPurchased] = useState(true)
  
  const [usersList, setUsersList] = useState<NewUser[]>([])
  const [loginTrack, setLoginTrack] = useState<UserLoginTrack[]>([])
  const [certificationTrack, setCertificationTrack] = useState<CertificationUser[]>([])

  const [searchEmail, setSearchEmail] = useState('')
  const [searchType, setSearchType] = useState<'user' | 'certification'>('user')

  const [showAccessModal, setShowAccessModal] = useState(false)
  const [isBulkMode, setIsBulkMode] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50)
  const [hasMoreData, setHasMoreData] = useState(true)

  useEffect(() => {
    loadStats()
    loadAllUsers()
  }, [])

  const loadStats = async () => {
    try {
      const [regCount, purCount, wkCount] = await Promise.all([
        AdminStatsClientService.getRegisteredCount(),
        AdminStatsClientService.getPurchasedCount(),
        AdminStatsClientService.getLastWeekCount()
      ])

      setStats({
        regCount: AdminStatsClientService.formatNumber(regCount),
        purCount: AdminStatsClientService.formatNumber(purCount),
        wkCount: AdminStatsClientService.formatNumber(wkCount)
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const loadAllUsers = async () => {
    setLoading(true)
    try {
      const users = await AdminUsersClientService.getAllRegisteredUsers(0, 50)
      setUsersList(users)
      setSelectedView('all')
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadPurchasedUsers = async () => {
    setLoading(true)
    try {
      const users = await AdminUsersClientService.getPurchasedUsers(showPurchased)
      setUsersList(users)
      setSelectedView('purchased')
      
      if (showPurchased) {
        setPurchasedBoxText('Total Purchases')
      } else {
        setPurchasedBoxText('Total Not Purchased')
        const notPurCount = await AdminStatsClientService.getNotPurchasedCount()
        setStats(prev => ({ ...prev, purCount: AdminStatsClientService.formatNumber(notPurCount) }))
      }
    } catch (error) {
      console.error('Error loading purchased users:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadLastWeekUsers = async () => {
    setLoading(true)
    try {
      const users = await AdminUsersClientService.getLastWeekUsers()
      setUsersList(users)
      setSelectedView('lastweek')
    } catch (error) {
      console.error('Error loading last week users:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadLoginData = async (page: number = 1) => {
    setLoading(true)
    try {
      const loginData = await AdminUsersClientService.getUserLoginData(page)
      setLoginTrack(loginData)
      setSelectedView('login')
      setCurrentPage(page)
      setHasMoreData(loginData.length === itemsPerPage)
    } catch (error) {
      console.error('Error loading login data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadCertificationData = async (page: number = 1) => {
    setLoading(true)
    try {
      const certData = await AdminUsersClientService.getCertificationData(page)
      setCertificationTrack(certData)
      setSelectedView('certification')
      setCurrentPage(page)
      setHasMoreData(certData.length === itemsPerPage)
    } catch (error) {
      console.error('Error loading certification data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchEmail.trim()) return
    
    setLoading(true)
    try {
      if (searchType === 'user') {
        const user = await AdminUsersClientService.searchUser(searchEmail)
        setUsersList(user ? [user] : [])
      } else {
        const certUser = await AdminUsersClientService.searchCertificationUser(searchEmail)
        setCertificationTrack(certUser ? [certUser] : [])
      }
    } catch (error) {
      console.error('Error searching:', error)
    } finally {
      setLoading(false)
    }
  }

  const togglePurchasedFilter = () => {
    setShowPurchased(!showPurchased)
    if (selectedView === 'purchased') {
      loadPurchasedUsers()
    }
  }

  const handleGrantAccess = () => {
    setIsBulkMode(false)
    setShowAccessModal(true)
  }

  const handleBulkGrantAccess = () => {
    setIsBulkMode(true)
    setShowAccessModal(true)
  }

  const handleAccessModalSuccess = () => {
    // Refresh current view
    if (selectedView === 'all') {
      loadAllUsers()
    } else if (selectedView === 'purchased') {
      loadPurchasedUsers()
    } else if (selectedView === 'lastweek') {
      loadLastWeekUsers()
    }
    loadStats()
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card 
          className={`cursor-pointer transition-colors ${selectedView === 'all' ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'}`}
          onClick={loadAllUsers}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Registrations</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.regCount}</div>
          </CardContent>
        </Card>

        <Card 
          className={`cursor-pointer transition-colors ${selectedView === 'purchased' ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'}`}
          onClick={loadPurchasedUsers}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{purchasedBoxText}</CardTitle>
            <div className="flex items-center space-x-2">
              {showPurchased ? <UserCheck className="h-4 w-4 text-green-600" /> : <UserX className="h-4 w-4 text-red-600" />}
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  togglePurchasedFilter()
                }}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.purCount}</div>
          </CardContent>
        </Card>

        <Card 
          className={`cursor-pointer transition-colors ${selectedView === 'lastweek' ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'}`}
          onClick={loadLastWeekUsers}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Week Registrations</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.wkCount}</div>
          </CardContent>
        </Card>

        <div className="space-y-2">
          <Button
            onClick={() => loadLoginData(1)}
            variant={selectedView === 'login' ? 'default' : 'outline'}
            className="w-full"
          >
            Get Login Data
          </Button>
          <Button
            onClick={() => loadCertificationData(1)}
            variant={selectedView === 'certification' ? 'default' : 'outline'}
            className="w-full"
          >
            Certification
          </Button>
        </div>
      </div>

      {/* Access Management Controls */}
      <div className="flex space-x-4">
        <Button
          onClick={handleGrantAccess}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Shield className="h-4 w-4" />
          <span>Grant Access</span>
        </Button>
        <Button
          onClick={handleBulkGrantAccess}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <ShieldCheck className="h-4 w-4" />
          <span>Bulk Grant Access</span>
        </Button>
      </div>

      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle>Search Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Input
              placeholder="Enter email address"
              value={searchEmail}
              onChange={(e) => setSearchEmail(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
            <select 
              value={searchType} 
              onChange={(e) => setSearchType(e.target.value as 'user' | 'certification')}
              className="px-3 py-2 border rounded-md"
            >
              <option value="user">User</option>
              <option value="certification">Certification</option>
            </select>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Login Tracking Data */}
      {!loading && selectedView === 'login' && loginTrack.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Login Tracking Data ({loginTrack.length})</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadLoginData(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm text-gray-600">Page {currentPage}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadLoginData(currentPage + 1)}
                disabled={!hasMoreData}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">IP Address</th>
                    <th className="text-left p-2">Browser</th>
                    <th className="text-left p-2">OS</th>
                    <th className="text-left p-2">Platform</th>
                    <th className="text-left p-2">Latitude</th>
                    <th className="text-left p-2">Longitude</th>
                    <th className="text-left p-2">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {loginTrack.map((data, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="p-2">{data.email}</td>
                      <td className="p-2">{data.last_ip}</td>
                      <td className="p-2">{data.browser}</td>
                      <td className="p-2">{data.os}</td>
                      <td className="p-2">{data.platform}</td>
                      <td className="p-2">{data.latitude}</td>
                      <td className="p-2">{data.longitude}</td>
                      <td className="p-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            const url = `https://www.google.com/maps?q=${data.latitude},${data.longitude}`
                            window.open(url, '_blank')
                          }}
                        >
                          Map
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Certification Data */}
      {!loading && selectedView === 'certification' && certificationTrack.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Certification Data ({certificationTrack.length})</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadCertificationData(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm text-gray-600">Page {currentPage}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadCertificationData(currentPage + 1)}
                disabled={!hasMoreData}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Certificate ID</th>
                    <th className="text-left p-2">Filename</th>
                    <th className="text-left p-2">Category</th>
                    {/* <th className="text-left p-2">Granted On</th> */}
                  </tr>
                </thead>
                <tbody>
                  {certificationTrack.map((data, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="p-2">{data.email}</td>
                      <td className="p-2">{data.certificateId}</td>
                      <td className="p-2">{data.filename}</td>
                      <td className="p-2">
                        <Badge variant={data.category ? 'secondary' : 'default'}>
                          {data.category || 'No Category'}
                        </Badge>
                      </td>
                      {/* <td className="p-2">{data.grantedOn}</td> */}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Users List */}
      {!loading && selectedView !== 'login' && selectedView !== 'certification' && usersList.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Users ({usersList.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Email</th>
                    <th className="text-left p-2">Institution</th>
                    <th className="text-left p-2">Branch</th>
                    <th className="text-left p-2">Phone</th>
                    <th className="text-left p-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {usersList.map((user, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="p-2">{user.f_name} {user.l_name}</td>
                      <td className="p-2">{user.email}</td>
                      <td className="p-2">{user.inst_name}</td>
                      <td className="p-2">{user.branch}</td>
                      <td className="p-2">{user.phone_no}</td>
                      <td className="p-2">
                        <Badge variant={user.subscribed ? 'default' : 'secondary'}>
                          {user.subscribed ? 'Subscribed' : 'Not Subscribed'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && (
        (selectedView === 'login' && loginTrack.length === 0) ||
        (selectedView === 'certification' && certificationTrack.length === 0) ||
        (selectedView !== 'login' && selectedView !== 'certification' && usersList.length === 0)
      ) && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500">No data found</p>
          </CardContent>
        </Card>
      )}

      {/* User Access Modal */}
      <UserAccessModal
        isOpen={showAccessModal}
        onClose={() => setShowAccessModal(false)}
        isBulkMode={isBulkMode}
        onSuccess={handleAccessModalSuccess}
      />
    </div>
  )
}
