// app/(admin)/admin/page.tsx
import Link from 'next/link'
import { FileText, Users, BarChart3, Settings, LucideIcon } from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import AdminBreadcrumb from '@/components/admin/admin-breadcrumb'

interface AdminCard {
  id: string
  title: string
  description: string
  content: string
  icon: LucideIcon
  iconColor: string
  href?: string
  isActive: boolean
}

interface QuickStat {
  id: string
  title: string
  value: string
  subtitle: string
}

const adminCards: AdminCard[] = [
  {
    id: 'tests-papers',
    title: 'Tests & Papers',
    description: 'Manage test papers and question banks',
    content:
      'Create, edit, and organize different types of test papers for your students.',
    icon: FileText,
    iconColor: 'text-blue-600',
    href: '/admin/tests/papers',
    isActive: true
  },
  {
    id: 'user-management',
    title: 'User Management',
    description: 'Manage students and instructors',
    content:
      'Coming soon: Manage user accounts, permissions, and access levels.',
    icon: Users,
    iconColor: 'text-green-600',
    isActive: false
  },
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'View performance metrics and reports',
    content: 'Coming soon: Detailed analytics and performance insights.',
    icon: BarChart3,
    iconColor: 'text-sky-600',
    isActive: false
  },
  {
    id: 'system-settings',
    title: 'System Settings',
    description: 'Configure platform settings',
    content: 'Coming soon: System configuration and general settings.',
    icon: Settings,
    iconColor: 'text-orange-600',
    isActive: false
  }
]

const quickStats: QuickStat[] = [
  {
    id: 'total-papers',
    title: 'Total Papers',
    value: '-',
    subtitle: 'Across all types'
  },
  {
    id: 'active-users',
    title: 'Active Users',
    value: '-',
    subtitle: 'Last 30 days'
  },
  {
    id: 'test-attempts',
    title: 'Test Attempts',
    value: '-',
    subtitle: 'This month'
  }
]

export default function AdminDashboard() {
  return (
    <div className="px-4 py-6">
      <AdminBreadcrumb
        items={[{ label: 'Admin Console', isCurrentPage: true }]}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {adminCards.map((card) => {
          const IconComponent = card.icon
          const CardWrapper = card.isActive && card.href ? Link : 'div'

          return (
            <CardWrapper key={card.id} href={card.href || ''}>
              <Card
                className={`h-full transition-shadow ${
                  card.isActive
                    ? 'hover:shadow-lg cursor-pointer'
                    : 'opacity-50'
                }`}
              >
                <CardHeader className="flex flex-col items-start">
                  <CardTitle className="flex items-center space-x-2">
                    <IconComponent className={`h-5 w-5 ${card.iconColor}`} />
                    <span>{card.title}</span>
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600 mt-1">
                    {card.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{card.content}</p>
                </CardContent>
              </Card>
            </CardWrapper>
          )
        })}
      </div>

      {/* Quick Stats */}
      <div className="mt-12">
        <h2 className="text-xl font-semibold mb-6">Quick Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickStats.map((stat) => (
            <Card key={stat.id}>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-gray-600">{stat.subtitle}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
