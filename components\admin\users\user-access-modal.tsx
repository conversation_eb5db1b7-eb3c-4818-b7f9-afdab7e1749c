'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { AdminUsersClientService } from '@/lib/client-services/admin/users.client'
import { UserAccessResponse, BulkGrantResult } from '@/types/user-types'

interface UserAccessModalProps {
  isOpen: boolean
  onClose: () => void
  isBulkMode: boolean
  onSuccess: () => void
}

export default function UserAccessModal({ 
  isOpen, 
  onClose, 
  isBulkMode, 
  onSuccess 
}: UserAccessModalProps) {
  const [email, setEmail] = useState('')
  const [bulkEmails, setBulkEmails] = useState('')
  const [loading, setLoading] = useState(false)
  const [userInfo, setUserInfo] = useState<UserAccessResponse | null>(null)
  const [bulkResults, setBulkResults] = useState<BulkGrantResult[]>([])
  const [showResults, setShowResults] = useState(false)
  const [error, setError] = useState('')

  const handleClose = () => {
    setEmail('')
    setBulkEmails('')
    setUserInfo(null)
    setBulkResults([])
    setShowResults(false)
    setError('')
    onClose()
  }

  const handleViewAccess = async () => {
    if (!email.trim()) return

    setLoading(true)
    setError('')
    try {
      const response = await AdminUsersClientService.getUserAccess(email)
      if (response.msg === 'not found') {
        setError('Email not found. Please ask them to register first.')
        setUserInfo(null)
      } else {
        setUserInfo(response)
      }
    } catch (error) {
      setError('Error fetching user access information')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGrantAccess = async () => {
    if (!email.trim()) return

    setLoading(true)
    try {
      const response = await AdminUsersClientService.grantUserAccess(email)
      if (response.msg === 'granted') {
        setShowResults(true)
        onSuccess()
        // Refresh user info
        await handleViewAccess()
      } else if (response.msg === 'not found') {
        setError('Email not found. Please ask them to register first.')
      } else {
        setError('Failed to grant access')
      }
    } catch (error) {
      setError('Error granting access')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleBulkGrant = async () => {
    if (!bulkEmails.trim()) return

    setLoading(true)
    setError('')
    try {
      const emailList = bulkEmails
        .split('\n')
        .map(email => email.trim())
        .filter(email => email.length > 0)

      const results = await AdminUsersClientService.bulkGrantAccess(emailList)
      setBulkResults(results)
      setShowResults(true)
      onSuccess()
    } catch (error) {
      setError('Error processing bulk grant')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isBulkMode ? 'Bulk Grant Access' : 'Grant User Access'}
          </DialogTitle>
          <DialogDescription>
            {isBulkMode 
              ? 'Grant access to multiple users by entering their email addresses'
              : 'View and manage user access permissions'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!isBulkMode ? (
            // Single User Mode
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <div className="flex space-x-2 mt-1">
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleViewAccess()}
                  />
                  <Button 
                    onClick={handleViewAccess} 
                    disabled={loading || !email.trim()}
                  >
                    View Access
                  </Button>
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              {userInfo && userInfo.msg !== 'not found' && (
                <div className="border rounded-lg p-4 bg-gray-50">
                  <h4 className="font-medium mb-2">User Information</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Email:</span>
                      <p>{email}</p>
                    </div>
                    <div>
                      <span className="font-medium">Name:</span>
                      <p>{userInfo.name || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="font-medium">Access Level:</span>
                      <p>
                        <Badge variant={userInfo.level === 'purchased' ? 'default' : 'secondary'}>
                          {userInfo.level || 'Basic'}
                        </Badge>
                      </p>
                    </div>
                  </div>
                  
                  {userInfo.level !== 'purchased' && (
                    <div className="mt-4">
                      <Button onClick={handleGrantAccess} disabled={loading}>
                        {loading ? 'Granting...' : 'Elevate to Purchased'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            // Bulk Mode
            <div className="space-y-4">
              <div>
                <Label htmlFor="bulkEmails">Email Addresses (one per line)</Label>
                <Textarea
                  id="bulkEmails"
                  placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                  value={bulkEmails}
                  onChange={(e) => setBulkEmails(e.target.value)}
                  rows={6}
                  className="mt-1"
                />
              </div>

              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <Button 
                onClick={handleBulkGrant} 
                disabled={loading || !bulkEmails.trim()}
                className="w-full"
              >
                {loading ? 'Processing...' : 'Grant Access to All'}
              </Button>
            </div>
          )}

          {/* Results Display */}
          {showResults && (
            <div className="border-t pt-4">
              <h4 className="font-medium mb-2">
                {isBulkMode ? 'Bulk Grant Results' : 'Success'}
              </h4>
              
              {!isBulkMode ? (
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-600 text-sm">Access granted successfully!</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {bulkResults.map((result, index) => (
                    <div 
                      key={index}
                      className={`p-2 rounded text-sm ${
                        result.status === 'success' 
                          ? 'bg-green-50 border border-green-200' 
                          : 'bg-red-50 border border-red-200'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span>{result.email}</span>
                        <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                          {result.status}
                        </Badge>
                      </div>
                      {result.message && (
                        <p className="text-xs mt-1 opacity-75">{result.message}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
