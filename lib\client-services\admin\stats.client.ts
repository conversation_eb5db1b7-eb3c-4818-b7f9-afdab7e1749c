// lib/client-services/admin/stats.client.ts
import axios from 'axios'
import { LoginRefresh } from '../../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'

/**
 * Create auth headers with JW<PERSON> token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class AdminStatsClientService {
  /**
   * Get total registered users count
   */
  static async getRegisteredCount(): Promise<string> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/stats/registered`,
        createAuthHeaders()
      )
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching registered count:', error)
      return '0'
    }
  }

  /**
   * Get total purchased users count
   */
  static async getPurchasedCount(): Promise<string> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/stats/purchased`,
        createAuthHeaders()
      )
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching purchased count:', error)
      return '0'
    }
  }

  /**
   * Get not purchased users count
   */
  static async getNotPurchasedCount(): Promise<string> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/stats/not/purchased`,
        createAuthHeaders()
      )
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching not purchased count:', error)
      return '0'
    }
  }

  /**
   * Get last week registrations count
   */
  static async getLastWeekCount(): Promise<string> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/stats/week/before`,
        createAuthHeaders()
      )
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching last week count:', error)
      return '0'
    }
  }

  /**
   * Get tests count
   */
  static async getTestsCount(): Promise<string> {
    try {
      const response = await axios.get(`${API_BASE_URL}/stats/tests`)
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching tests count:', error)
      return '0'
    }
  }

  /**
   * Get answered questions count
   */
  static async getAnswersCount(): Promise<string> {
    try {
      const response = await axios.get(`${API_BASE_URL}/stats/answered`)
      return response.data?.text || '0'
    } catch (error) {
      console.error('Error fetching answers count:', error)
      return '0'
    }
  }

  /**
   * Format number with commas for display
   */
  static formatNumber(num: string): string {
    return num.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }
}
