'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { AdminUsersClientService } from '@/lib/client-services/admin/users.client'

interface AddCertificationModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export default function AddCertificationModal({ 
  isOpen, 
  onClose, 
  onSuccess 
}: AddCertificationModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    certificateId: '',
    filename: '',
    grantedOn: new Date().toISOString().split('T')[0] // Today's date in YYYY-MM-DD format
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const handleClose = () => {
    setFormData({
      email: '',
      certificateId: '',
      filename: '',
      grantedOn: new Date().toISOString().split('T')[0]
    })
    setError('')
    setSuccess(false)
    onClose()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.email.trim() || !formData.certificateId.trim() || !formData.filename.trim()) {
      setError('Please fill in all required fields')
      return
    }

    setLoading(true)
    setError('')
    
    try {
      const response = await AdminUsersClientService.addCertificationUser(formData)
      if (response.success) {
        setSuccess(true)
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        setError(response.message || 'Failed to add certification user')
      }
    } catch (error) {
      setError('Error adding certification user')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add User to Certification</DialogTitle>
          <DialogDescription>
            Add a new user to the certification tracking system
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="certificateId">Certificate ID *</Label>
            <Input
              id="certificateId"
              type="text"
              placeholder="CERT-2024-001"
              value={formData.certificateId}
              onChange={(e) => handleInputChange('certificateId', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="filename">Certificate Filename *</Label>
            <Input
              id="filename"
              type="text"
              placeholder="certificate.pdf"
              value={formData.filename}
              onChange={(e) => handleInputChange('filename', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="grantedOn">Granted On</Label>
            <Input
              id="grantedOn"
              type="date"
              value={formData.grantedOn}
              onChange={(e) => handleInputChange('grantedOn', e.target.value)}
            />
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-600 text-sm">
                Certification user added successfully!
              </p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
