// types/company-paper-types.ts

export interface ChapterPaper {
  paper_id: string
  paper_name: string
  time_lim: number
  no_of_ques: number
}

export interface CompanyData {
  id: number
  name: string
  logo: string
  alt: string
}

export interface CompanyPaperSubmission {
  email: string
  paper_id: string
  marks: number
  answered_on: string
}

export interface CompanyTestsListProps {
  lockedResource: boolean
  onCompanySelect: (companyType: number) => void
  onNavigateToPlans: () => void
}

export interface CompanyPapersDetailProps {
  papers: ChapterPaper[]
  companyType: number
  pageHeading: string
  isLoading: boolean
  onBeginTest: (paperId: string, paperName: string, paperLim: number) => void
}

// Additional interfaces for the complete company papers system
export interface CompanyInfo {
  id: number
  name: string
  logo: string
  alt: string
  regex: RegExp
  description?: string
}

export interface CompanyPapers {
  paper_id: string
  paper_name: string
  company: string
  paper_year: string
  time_lim: number
  no_of_ques: number
  public: string
}

export interface UserPermissionResponse {
  msg: string
}

// Company data with all companies
export const COMPANY_DATA: CompanyInfo[] = [
  {
    id: 1,
    name: 'Infosys',
    logo: '/img/companylogos/infosys-logo-JPEG.png',
    alt: 'Infosys Papers',
    regex: /^INFOSYS*/
  },
  {
    id: 2,
    name: 'Accenture',
    logo: '/img/companylogos/Accenture-logo.png',
    alt: 'Accenture Papers',
    regex: /^ACCENTURE*/
  },
  {
    id: 4,
    name: 'TCS-NQT (Previous Pattern)',
    logo: '/img/companylogos/tcs-logo.png',
    alt: 'TCS NQT Papers',
    regex: /^TCS (NQT||DIGITAL)*/
  },
  {
    id: 5,
    name: 'Wipro',
    logo: '/img/companylogos/wipro.png',
    alt: 'Wipro Papers',
    regex: /^WIPRO MOCK*/
  },
  {
    id: 6,
    name: 'Cognizant',
    logo: '/img/companylogos/cognizant.svg',
    alt: 'Cognizant Papers',
    regex: /^Cognizant*/
  },
  {
    id: 7,
    name: 'Capgemini',
    logo: '/img/companylogos/capgemini.png',
    alt: 'Capgemini Papers',
    regex: /^Capgemini*/
  },
  {
    id: 8,
    name: 'Tech Mahindra',
    logo: '/img/companylogos/tech-mahindra.svg',
    alt: 'Tech Mahindra Papers',
    regex: /^Tech Mahindra*/
  },
  {
    id: 9,
    name: 'Zoho',
    logo: '/img/companylogos/zoho.svg',
    alt: 'Zoho Papers',
    regex: /^Zoho*/
  },
  {
    id: 10,
    name: 'HCL',
    logo: '/img/companylogos/HCL.jpg',
    alt: 'HCL Papers',
    regex: /^HCL*/
  },
  {
    id: 11,
    name: 'NTT',
    logo: '/img/companylogos/ntt.jpg',
    alt: 'NTT Papers',
    regex: /^NTT*/
  },
  {
    id: 12,
    name: 'Oracle',
    logo: '/img/companylogos/oracle.png',
    alt: 'Oracle Papers',
    regex: /^ORACLE*/
  },
  {
    id: 13,
    name: 'DXC Technology',
    logo: '/img/companylogos/dxcTech.png',
    alt: 'DXC Technology Papers',
    regex: /^DXC*/
  },
  {
    id: 14,
    name: 'Deloitte',
    logo: '/img/companylogos/1280px-Deloitte.svg.png',
    alt: 'Deloitte Papers',
    regex: /^DELOITTE*/
  },
  {
    id: 15,
    name: 'L&T',
    logo: '/img/companylogos/landttech.jpg',
    alt: 'L&T Papers',
    regex: /^L&T*/
  },
  {
    id: 16,
    name: 'Mindtree',
    logo: '/img/companylogos/mindtree.jpg',
    alt: 'Mindtree Papers',
    regex: /^MINDTREE*/
  },
  {
    id: 17,
    name: 'Hexaware',
    logo: '/img/companylogos/PRNE_Hexaware_logo_Logo.jpg',
    alt: 'Hexaware Papers',
    regex: /^HEXAWARE*/
  },
  {
    id: 18,
    name: 'Mphasis',
    logo: '/img/companylogos/mphasis.jpeg',
    alt: 'Mphasis Papers',
    regex: /^MPHASIS*/
  },
  {
    id: 19,
    name: 'SLK Software',
    logo: '/img/companylogos/slk.png',
    alt: 'SLK Software Papers',
    regex: /^SLK SOFTWARE*/
  },
  {
    id: 20,
    name: 'HSBC',
    logo: '/img/companylogos/client-hsbc.jpg',
    alt: 'HSBC Papers',
    regex: /^HSBC*/
  },
  {
    id: 21,
    name: 'VM Ware',
    logo: '/img/companylogos/vmware-logo.svg',
    alt: 'VM Ware Papers',
    regex: /^VM WARE*/
  },
  {
    id: 22,
    name: 'Wipro WILP',
    logo: '/img/companylogos/wipro.png',
    alt: 'Wipro WILP Papers',
    regex: /^WIPRO WILP*/
  },
  {
    id: 23,
    name: 'Wipro NLTH',
    logo: '/img/companylogos/wipro.png',
    alt: 'Wipro NLTH Papers',
    regex: /^WIPRO NLTH*/
  },
  {
    id: 24,
    name: 'Amazon',
    logo: '/img/companylogos/Amazon-Logo_Feature.png',
    alt: 'Amazon Papers',
    regex: /^AMAZON*/
  },
  {
    id: 25,
    name: 'Goldman Sachs',
    logo: '/img/companylogos/Goldman_Sachs.svg.png',
    alt: 'Goldman Sachs Papers',
    regex: /^GOLDMAN SACHS*/
  },
  {
    id: 26,
    name: 'Qualcomm',
    logo: '/img/companylogos/Qualcomm-emblem.png',
    alt: 'Qualcomm Papers',
    regex: /^QUALCOMM*/
  },
  {
    id: 27,
    name: 'Dell',
    logo: '/img/companylogos/delllogo.png',
    alt: 'Dell Papers',
    regex: /^DELL*/
  },
  {
    id: 28,
    name: 'Wells Fargo',
    logo: '/img/companylogos/Wells_Fargo_Logo.png',
    alt: 'Wells Fargo Papers',
    regex: /^WELLS FARGO*/
  },
  {
    id: 29,
    name: 'Aricent',
    logo: '/img/companylogos/1200px-Aricent_Logo.jpg',
    alt: 'Aricent Papers',
    regex: /^ARICENT*/
  },
  {
    id: 30,
    name: 'Amdocs',
    logo: '/img/companylogos/amdocs_logo.png',
    alt: 'Amdocs Papers',
    regex: /^AMDOCS*/
  },
  {
    id: 31,
    name: 'Cisco',
    logo: '/img/companylogos/cisco-emblem.jpg',
    alt: 'Cisco Papers',
    regex: /^CISCO*/
  },
  {
    id: 32,
    name: 'CGI',
    logo: '/img/companylogos/cgilogo.png',
    alt: 'CGI Papers',
    regex: /^CGI*/
  },
  {
    id: 33,
    name: 'Sapient',
    logo: '/img/companylogos/sapient.png',
    alt: 'Sapient Papers',
    regex: /^SAPIENT*/
  },
  {
    id: 34,
    name: 'Atos',
    logo: '/img/companylogos/atos.png',
    alt: 'Atos Papers',
    regex: /^ATOS*/
  },
  {
    id: 35,
    name: 'PWC',
    logo: '/img/companylogos/PwC-Logo.jpg',
    alt: 'PWC Papers',
    regex: /^PWC*/
  },
  {
    id: 36,
    name: 'Adobe',
    logo: '/img/companylogos/adobe-logo-492427.png',
    alt: 'Adobe Papers',
    regex: /^ADOBE*/
  },
  {
    id: 37,
    name: 'TCS-NQT 2022 (Latest Pattern)',
    logo: '/img/companylogos/tcs-logo.png',
    alt: 'TCS 2022 Papers',
    regex: /^TCS-NQT 2022*/
  },
  {
    id: 38,
    name: 'KPMG',
    logo: '/img/companylogos/kpmg.png',
    alt: 'KPMG Papers',
    regex: /^KPMG*/
  },
  {
    id: 39,
    name: 'Syntel',
    logo: '/img/companylogos/Syntel.png',
    alt: 'Syntel Papers',
    regex: /^SYNTEL*/
  },
  {
    id: 40,
    name: 'Ericsson',
    logo: '/img/companylogos/Ericsso.png',
    alt: 'Ericsson Papers',
    regex: /^ERICSSON*/
  },
  {
    id: 41,
    name: 'Comviva',
    logo: '/img/companylogos/Comviva.png',
    alt: 'Comviva Papers',
    regex: /^COMVIVA*/
  },
  {
    id: 42,
    name: 'EY',
    logo: '/img/companylogos/EY.png',
    alt: 'EY Papers',
    regex: /^EY*/
  },
  {
    id: 43,
    name: 'Genpact',
    logo: '/img/companylogos/Genpact.png',
    alt: 'Genpact Papers',
    regex: /^GENPACT*/
  },
  {
    id: 44,
    name: 'Verizon',
    logo: '/img/companylogos/Verizon.jpg',
    alt: 'Verizon Papers',
    regex: /^VERIZON*/
  },
  {
    id: 45,
    name: 'HP',
    logo: '/img/companylogos/hp-logo.jpg',
    alt: 'HP Papers',
    regex: /^HP*/
  },
  {
    id: 46,
    name: 'KPIT',
    logo: '/img/companylogos/KPIT_logo.jpg',
    alt: 'KPIT Papers',
    regex: /^KPIT*/
  },
  {
    id: 47,
    name: 'Musigma',
    logo: '/img/companylogos/musigma.jpg',
    alt: 'Musigma Papers',
    regex: /^MUSIGMA*/
  },
  {
    id: 48,
    name: 'Honeywell',
    logo: '/img/companylogos/honeywell-logo.jpg',
    alt: 'Honeywell Papers',
    regex: /^HONEYWELL*/
  },
  {
    id: 49,
    name: 'Intel',
    logo: '/img/companylogos/Intel_logo.png',
    alt: 'Intel Papers',
    regex: /^INTEL*/
  },
  {
    id: 50,
    name: 'Torri Harris',
    logo: '/img/companylogos/Torri_Harris_Logo.jpg',
    alt: 'Torri Harris Papers',
    regex: /^TORRI HARRIS*/
  },
  {
    id: 51,
    name: 'Siemens',
    logo: '/img/companylogos/Siemens_Logo.png',
    alt: 'Siemens Papers',
    regex: /^SIEMENS*/
  },
  {
    id: 52,
    name: 'Tata Elxsi',
    logo: '/img/companylogos/Tata-Elxsi-Logo.jpg',
    alt: 'Tata Elxsi Papers',
    regex: /^TATA ELXSI*/
  },
  {
    id: 53,
    name: 'Persistent Systems',
    logo: '/img/companylogos/Persistent_Systems_Logo.jpg',
    alt: 'Persistent Systems Papers',
    regex: /^PERSISTENT*/
  },
  {
    id: 54,
    name: 'HDFC',
    logo: '/img/companylogos/HDFC.jpg',
    alt: 'HDFC Papers',
    regex: /^HDFC*/
  },
  {
    id: 55,
    name: 'Sonata Software',
    logo: '/img/companylogos/sonata-software.jpg',
    alt: 'Sonata Software Papers',
    regex: /^SONATA SOFTWARE*/
  },
  {
    id: 56,
    name: 'Harman',
    logo: '/img/companylogos/Harman_International_logo.svg',
    alt: 'Harman Papers',
    regex: /^HARMAN*/
  },
  {
    id: 57,
    name: 'Reliance Jio',
    logo: '/img/companylogos/Jio-Logo.jpg',
    alt: 'Reliance Jio Papers',
    regex: /^RELIANCE JIO*/
  },
  {
    id: 58,
    name: 'Samsung',
    logo: '/img/companylogos/Samsung-Logo.jpg',
    alt: 'Samsung Papers',
    regex: /^SAMSUNG*/
  },
  {
    id: 59,
    name: 'Birlasoft',
    logo: '/img/companylogos/birlasoft-logo.jpg',
    alt: 'Birlasoft Papers',
    regex: /^BIRLASOFT*/
  },
  {
    id: 60,
    name: 'ITC Infotech',
    logo: '/img/companylogos/ITC_Infotech_Logo.jpg',
    alt: 'ITC Infotech Papers',
    regex: /^ITC INFOTECH*/
  },
  {
    id: 61,
    name: 'ADP',
    logo: '/img/companylogos/ADP.jpg',
    alt: 'ADP Papers',
    regex: /^ADP*/
  },
  {
    id: 62,
    name: 'Cyient',
    logo: '/img/companylogos/cyient.png',
    alt: 'Cyient Papers',
    regex: /^CYIENT*/
  },
  {
    id: 63,
    name: 'Robert Bosch',
    logo: '/img/companylogos/Bosch-Logo.jpg',
    alt: 'Robert Bosch Papers',
    regex: /^ROBERT BOSCH*/
  },
  {
    id: 64,
    name: 'JP Morgan',
    logo: '/img/companylogos/JP_Morgan.png',
    alt: 'JP Morgan Papers',
    regex: /^JP MORGAN*/
  },
  {
    id: 65,
    name: 'Maruthi Suzuki',
    logo: '/img/companylogos/Maruti-Suzuki-Logo.jpg',
    alt: 'Maruthi Suzuki Papers',
    regex: /^MARUTHI SUZUKI*/
  },
  {
    id: 66,
    name: 'Tata Power',
    logo: '/img/companylogos/Tata_Power_Logo.png',
    alt: 'Tata Power Papers',
    regex: /^TATA POWER*/
  },
  {
    id: 67,
    name: 'Happiest Minds',
    logo: '/img/companylogos/HappiestMinds_Logo.png',
    alt: 'Happiest Minds Papers',
    regex: /^HAPPIEST MINDS*/
  },
  {
    id: 68,
    name: 'Sasken',
    logo: '/img/companylogos/Sasken.png',
    alt: 'Sasken Papers',
    regex: /^SASKEN*/
  },
  {
    id: 69,
    name: 'LTI',
    logo: '/img/companylogos/landttech.jpg',
    alt: 'LTI Papers',
    regex: /^LTI*/
  },
  {
    id: 70,
    name: 'LG',
    logo: '/img/companylogos/LG.png',
    alt: 'LG Papers',
    regex: /^LG*/
  },
  {
    id: 71,
    name: 'Vedanta',
    logo: '/img/companylogos/vedanta.png',
    alt: 'Vedanta Papers',
    regex: /^VENDATA*/
  },
  {
    id: 72,
    name: 'Newgen Technologies',
    logo: '/img/companylogos/newgen.svg',
    alt: 'Newgen Technologies Papers',
    regex: /^NEWGEN TECHNOLOGIES*/
  },
  {
    id: 73,
    name: 'Zensar',
    logo: '/img/companylogos/zensar.jpg',
    alt: 'Zensar Papers',
    regex: /^ZENSAR*/
  },
  {
    id: 74,
    name: 'Tavant Technologies',
    logo: '/img/companylogos/tavant.jpg',
    alt: 'Tavant Technologies Papers',
    regex: /^TAVANT TECHNOLOGIES*/
  },
  {
    id: 75,
    name: 'TVS Motors',
    logo: '/img/companylogos/TVS.jpg',
    alt: 'TVS Motors Papers',
    regex: /^TVS MOTORS*/
  },
  {
    id: 76,
    name: 'Philips',
    logo: '/img/companylogos/Philips.png',
    alt: 'Philips Papers',
    regex: /^PHILIPS*/
  },
  {
    id: 77,
    name: 'Ness Technologies',
    logo: '/img/companylogos/ness.png',
    alt: 'Ness Technologies Papers',
    regex: /^NESS TECHNOLOGIES*/
  },
  {
    id: 78,
    name: 'Tata Motors',
    logo: '/img/companylogos/tatamotors.png',
    alt: 'Tata Motors Papers',
    regex: /^TATA MOTORS*/
  },
  {
    id: 79,
    name: 'Tata Steel',
    logo: '/img/companylogos/tatasteel.png',
    alt: 'Tata Steel Papers',
    regex: /^TATA STEEL*/
  },
  {
    id: 80,
    name: 'Sony',
    logo: '/img/companylogos/sony.jpg',
    alt: 'Sony Papers',
    regex: /^SONY*/
  },
  {
    id: 81,
    name: 'Reliance Industries Limited',
    logo: '/img/companylogos/relianceIndustriesLimited.jpg',
    alt: 'Reliance Industries Limited Papers',
    regex: /^RELIANCE INDUSTRIES LIMITED*/
  },
  {
    id: 82,
    name: 'Sutherland Global Services',
    logo: '/img/companylogos/sutherlandglobalservices.png',
    alt: 'Sutherland Global Services Papers',
    regex: /^SUTHERLAND GLOBAL SERVICES*/
  },
  {
    id: 83,
    name: 'Axtria',
    logo: '/img/companylogos/axtria_logo.jpeg',
    alt: 'Axtria Papers',
    regex: /^AXTRIA*/
  },
  {
    id: 84,
    name: 'Expedia',
    logo: '/img/companylogos/Expedia.jpeg',
    alt: 'Expedia Papers',
    regex: /^EXPEDIA*/
  },
  {
    id: 85,
    name: 'General Motors',
    logo: '/img/companylogos/GeneralMotors.jpg',
    alt: 'General Motors Papers',
    regex: /^GENERAL MOTORS*/
  },
  {
    id: 86,
    name: 'Flextronics',
    logo: '/img/companylogos/Flextronics.jpg',
    alt: 'Flextronics Papers',
    regex: /^FLEXTRONICS*/
  },
  {
    id: 87,
    name: 'Jindal Steel',
    logo: '/img/companylogos/jindal_steel.png',
    alt: 'Jindal Steel Papers',
    regex: /^JINDAL STEEL*/
  },
  {
    id: 88,
    name: 'Nucleus Software',
    logo: '/img/companylogos/nucleus_software.png',
    alt: 'Nucleus Software Papers',
    regex: /^NUCLEUS SOFTWARE*/
  },
  {
    id: 89,
    name: 'Essar',
    logo: '/img/companylogos/essar.jpg',
    alt: 'Essar Papers',
    regex: /^ESSAR*/
  },
  {
    id: 90,
    name: 'Godrej',
    logo: '/img/companylogos/Godrej.png',
    alt: 'Godrej Papers',
    regex: /^GODREJ*/
  },
  {
    id: 91,
    name: 'Infogain',
    logo: '/img/companylogos/Infogain.png',
    alt: 'Infogain Papers',
    regex: /^INFOGAIN*/
  },
  {
    id: 92,
    name: 'Mazars',
    logo: '/img/companylogos/Mazars.png',
    alt: 'Mazars Papers',
    regex: /^MAZARS*/
  }
]

// Company type to heading mapping
export const getCompanyHeading = (companyType: number): string => {
  const company = COMPANY_DATA.find((company) => company.id === companyType)
  return company ? company.name : 'Company Papers'
}

// Company type to regex mapping
export const getCompanyRegex = (companyType: number): RegExp => {
  const regex = COMPANY_DATA.find((company) => company.id === companyType)
  return regex ? regex.regex : / /
}
